import React from 'react';
import { GroupedLineItemDetails } from '../hooks/useLineItemDetails';
import { StatementColumn } from '../types/statementTypes';
import {
  formatCurrency,
  formatPercentage,
  formatVariance,
  getTableCellStyle,
} from '../utils/formatting';

interface AccountDetailsRendererProps {
  lineItemName: string;
  categoryIndex: number;
  itemIndex: number;
  columns: StatementColumn[];
  lineItemDetails?: GroupedLineItemDetails;
}

const AccountDetailsRenderer: React.FC<AccountDetailsRendererProps> = ({
  lineItemName,
  categoryIndex,
  itemIndex,
  columns,
  lineItemDetails,
}) => {
  const accounts = lineItemDetails?.[lineItemName] || [];

  if (accounts.length === 0) {
    return (
      <tr className="bg-gray-50 border-b border-[#DEEFFF]">
        <td
          colSpan={columns.length}
          className="px-2 py-4 text-center text-gray-500"
        >
          No account details available
        </td>
      </tr>
    );
  }

  return (
    <>
      {accounts.map((account, accountIndex) => (
        <tr
          key={`${categoryIndex}-${itemIndex}-account-${accountIndex}`}
          className={`border-b border-[#DEEFFF] ${accountIndex % 2 === 1 ? 'bg-gray-50' : 'bg-white'}`}
        >
          {columns.map((column, colIndex) => {
            let cellContent = '';
            let cellClass =
              'px-2 py-2 text-sm border-r border-[#DEEFFF] last:border-r-0';

            switch (column.id) {
              case 'name':
                cellContent = `    ${account.AcctCodeAndDesc || account.acctCode || ''}`;
                cellClass += ' text-left pl-8';
                break;
              // YTD Report columns
              case 'actualYTD':
                cellContent = formatCurrency(account.PTD_Actual || 0);
                cellClass += ' text-right';
                break;
              case 'budgetYTD':
                cellContent = formatCurrency(account.PTD_Budget || 0);
                cellClass += ' text-right';
                break;
              case 'dollarVariance':
                cellContent = formatVariance(account.PTD_Variance || 0);
                cellClass += ' text-right';
                break;
              case 'percentVariance':
                cellContent = formatPercentage(account.PTD_Variance_Perct || 0);
                cellClass += ' text-right';
                break;
              case 'forecastFY':
                cellContent = formatCurrency(account.YTD_Actual || 0);
                cellClass += ' text-right';
                break;
              case 'budgetFY':
                cellContent = formatCurrency(account.YTD_Budget || 0);
                cellClass += ' text-right';
                break;
              case 'dollarVarianceFY':
                cellContent = formatVariance(account.YTD_Variance || 0);
                cellClass += ' text-right';
                break;
              case 'percentVarianceFY':
                cellContent = formatPercentage(account.YTD_Variance_Perct || 0);
                cellClass += ' text-right';
                break;
              // PTD Report columns
              case 'ptdActual':
                cellContent = formatCurrency(account.PTD_Actual || 0);
                cellClass += ' text-right';
                break;
              case 'ptdBudget':
                cellContent = formatCurrency(account.PTD_Budget || 0);
                cellClass += ' text-right';
                break;
              case 'ptdVariance':
                cellContent = formatVariance(account.PTD_Variance || 0);
                cellClass += ' text-right';
                break;
              case 'ptdVariancePercent':
                cellContent = formatPercentage(account.PTD_Variance_Perct || 0);
                cellClass += ' text-right';
                break;
              case 'ytdActual':
                cellContent = formatCurrency(account.YTD_Actual || 0);
                cellClass += ' text-right';
                break;
              case 'ytdBudget':
                cellContent = formatCurrency(account.YTD_Budget || 0);
                cellClass += ' text-right';
                break;
              case 'ytdVariance':
                cellContent = formatVariance(account.YTD_Variance || 0);
                cellClass += ' text-right';
                break;
              case 'ytdVariancePercent':
                cellContent = formatPercentage(account.YTD_Variance_Perct || 0);
                cellClass += ' text-right';
                break;
            }

            return (
              <td
                key={`${categoryIndex}-${itemIndex}-account-${accountIndex}-${column.id}`}
                className={cellClass}
                style={getTableCellStyle(colIndex)}
              >
                {cellContent}
              </td>
            );
          })}
        </tr>
      ))}
    </>
  );
};

export default AccountDetailsRenderer;
