import React from 'react';
import {
  BaseCategoryTotals,
  CategoryData,
  PTDCategoryData,
  PTDCategoryTotals,
  StatementColumn,
  ThousandCategoryData,
  ThousandCategoryTotals,
} from '../types/statementTypes';
import {
  getMarginCategoryBackgroundClass,
  getTableCellStyle,
} from '../utils/formatting';

interface TotalRowRendererProps {
  category: CategoryData | PTDCategoryData | ThousandCategoryData;
  categoryIndex: number;
  columns: StatementColumn[];
  convertTotalForReportTable: (
    total: BaseCategoryTotals | PTDCategoryTotals | ThousandCategoryTotals,
    categoryTitle?: string,
  ) => Record<string, number | string>;
  formatCellValue: (
    value: number | string,
    columnId: string,
    category?: CategoryData | PTDCategoryData | ThousandCategoryData,
  ) => string;
}

const TotalRowRenderer: React.FC<TotalRowRendererProps> = ({
  category,
  categoryIndex,
  columns,
  convertTotalForReportTable,
  formatCellValue,
}) => {
  if (category.title === 'RETURN ON REVENUE') return null;

  const totalData = convertTotalForReportTable(category.total, category.title);
  const backgroundClass = getMarginCategoryBackgroundClass(category.title);

  return (
    <tr
      className={`font-bold text-[#43298F] border-b border-[#DEEFFF] ${backgroundClass}`}
    >
      <td
        colSpan={
          columns.filter(
            (col) =>
              col.id === 'name' ||
              col.id === 'property' ||
              col.id === 'propertyAlias' ||
              col.id === 'rpm',
          ).length
        }
        className="px-2 py-0 whitespace-nowrap text-left font-bold border-r border-[#c7d2fe]"
      >
        <div className="flex items-center">{category.title}</div>
      </td>
      {columns
        .filter(
          (col) =>
            col.id !== 'name' &&
            col.id !== 'property' &&
            col.id !== 'propertyAlias' &&
            col.id !== 'rpm',
        )
        .map((column, index, array) => {
          const fullColumnIndex = columns.findIndex(
            (col) => col.id === column.id,
          );

          return (
            <td
              key={`total-${categoryIndex}-${column.id}`}
              className={`px-2 py-0 whitespace-nowrap text-right font-bold ${
                index < array.length - 1 ? 'border-r border-[#c7d2fe]' : ''
              }`}
              style={getTableCellStyle(fullColumnIndex)}
            >
              {formatCellValue(totalData[column.id], column.id, category)}
            </td>
          );
        })}
    </tr>
  );
};

export default TotalRowRenderer;
