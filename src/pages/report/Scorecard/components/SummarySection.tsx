import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { RESPONSIVE_FONTS } from '../constants/typography';
import LoadingSkeleton from './shared/LoadingSkeleton';

const SummarySection = () => {
  const { summaryTexts, loadingStates } = useSelector(
    (state: RootState) => state.scorecard,
  );
  const isLoading = loadingStates.summaryTexts;

  if (isLoading) {
    return <LoadingSkeleton type="summary" />;
  }

  return (
    <div className="h-full flex flex-col bg-white shadow-sm overflow-hidden">
      <div
        className="px-2 sm:px-3 py-1 sm:py-2"
        style={{ backgroundColor: '#8B8FE8' }}
      >
        <h3
          className={`font-semibold text-white ${RESPONSIVE_FONTS.tableContent} tracking-wide`}
        >
          Summary
        </h3>
      </div>
      <div className="flex-1 overflow-auto p-2 sm:p-4">
        <div className="max-w-prose">
          {summaryTexts.map((summary, index) => (
            <p
              key={index}
              className={`${RESPONSIVE_FONTS.summaryText} text-gray-700 leading-relaxed break-words`}
            >
              {summary.content}
            </p>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SummarySection;
