import { ReactNode, useEffect, useMemo, useState } from 'react';
import {
  staffingKpiMarketApi,
  staffingKpiRegionalApi,
  staffingKpiRPMApi,
} from '@/api/staffingKpi/staffingKpiApi';
import {
  StaffingKPIPayload,
  StaffingMarketKPITypes,
  StaffingRegionalKPITypes,
  StaffingRPMKPITypes,
} from '@/api/staffingKpi/staffingKpiApi.types';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { toast } from 'sonner';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { Container } from '@/components/common/container';
import PropertyPackageReportFilters from '@/pages/report/components/PropertyPackageReportFilters';
import {
  ReportsTabsList,
  ReportTabsContentUILayout,
  ReportTabsTrigger,
} from '../ReportsCommonComponents/reportsTabsUi/ReportsTabsAtoms';
import MarketLevelStaffingTable from './components/MarketLevelStaffingTable';
import PropertyCountStaffingTable from './components/PropertyCountStaffingTable';
import RegionalAverageTable from './components/RegionalAverageTable';

export default function StaffingKPIReportPage() {
  const { filters } = useSelector((state: RootState) => state.incomeReport);

  const [loading, setLoading] = useState({
    rpm: false,
    regional: false,
    market: false,
  });
  const [error, setError] = useState({
    rpm: '',
    regional: '',
    market: '',
  });

  const [staffingRPMData, setStaffingRPMData] = useState<StaffingRPMKPITypes[]>(
    [],
  );
  const [staffingRegionalData, setStaffingRegionalData] = useState<
    StaffingRegionalKPITypes[]
  >([]);
  const [staffingMarketData, setStaffingMarketData] = useState<
    StaffingMarketKPITypes[]
  >([]);

  const getMonthNumber = (monthName: string): string => {
    const months = {
      January: '1',
      February: '2',
      March: '3',
      April: '4',
      May: '5',
      June: '6',
      July: '7',
      August: '8',
      September: '9',
      October: '10',
      November: '11',
      December: '12',
    };

    return months[monthName as keyof typeof months] || '1';
  };

  const fetchRPMData = async (requestBody: StaffingKPIPayload) => {
    setLoading((prev) => ({ ...prev, rpm: true }));
    setError((prev) => ({ ...prev, rpm: '' }));
    try {
      const res = await staffingKpiRPMApi(requestBody);
      setStaffingRPMData(res.data);
    } catch {
      setError((prev) => ({ ...prev, rpm: 'Failed to fetch RPM data' }));
      toast.error('Failed to fetch RPM data');
    } finally {
      setLoading((prev) => ({ ...prev, rpm: false }));
    }
  };

  const fetchRegionalData = async (requestBody: StaffingKPIPayload) => {
    setLoading((prev) => ({ ...prev, regional: true }));
    setError((prev) => ({ ...prev, regional: '' }));
    try {
      const res = await staffingKpiRegionalApi(requestBody);
      setStaffingRegionalData(res.data);
    } catch {
      setError((prev) => ({
        ...prev,
        regional: 'Failed to fetch Regional data',
      }));
      toast.error('Failed to fetch Regional data');
    } finally {
      setLoading((prev) => ({ ...prev, regional: false }));
    }
  };

  const fetchMarketData = async (requestBody: StaffingKPIPayload) => {
    setLoading((prev) => ({ ...prev, market: true }));
    setError((prev) => ({ ...prev, market: '' }));
    try {
      const res = await staffingKpiMarketApi(requestBody);
      setStaffingMarketData(res.data);
    } catch {
      setError((prev) => ({ ...prev, market: 'Failed to fetch Market data' }));
      toast.error('Failed to fetch Market data');
    } finally {
      setLoading((prev) => ({ ...prev, market: false }));
    }
  };

  useEffect(() => {
    const monthNumbers =
      filters.month.length > 0
        ? filters.month.map((month) => getMonthNumber(month))
        : ['1'];
    const yearValue = filters.year || '2025';
    const requestBody = {
      year: yearValue,
      month: monthNumbers.length === 1 ? monthNumbers[0] : monthNumbers,
      department:
        filters.department.length === 0
          ? null
          : filters.department.length === 1
            ? filters.department[0]
            : filters.department,
      businessType:
        filters.businessType.length === 0
          ? null
          : filters.businessType.length === 1
            ? filters.businessType[0]
            : filters.businessType,
      marketleader:
        filters.marketLeader.length === 0
          ? null
          : filters.marketLeader.length === 1
            ? filters.marketLeader[0]
            : filters.marketLeader,
      adminBu:
        filters.adminBU.length === 0
          ? null
          : filters.adminBU.length === 1
            ? filters.adminBU[0]
            : filters.adminBU,
    };

    fetchRPMData(requestBody);
    fetchRegionalData(requestBody);
    fetchMarketData(requestBody);
  }, [filters]);

  const datePeriod = useMemo(() => {
    const year = filters.year || '';
    const months = filters.month || [];

    if (months.length === 1) {
      return `${months[0]} ${year}`;
    } else if (months.length > 1) {
      return `${months[0]} - ${months[months.length - 1]} ${year}`;
    } else {
      return year;
    }
  }, [filters]);

  return (
    <Container title="Staffing KPI" width="fluid">
      <div className="bg-white shadow-md rounded-lg p-4">
        <div className="mb-6">
          <PropertyPackageReportFilters businessFilter={false}/>
        </div>

        <Tabs defaultValue="propertyCount">
          <ReportsTabsList className="grid-cols-3">
            <ReportTabsTrigger
              value="propertyCount"
              tabName="Properties-RPM"
            />
            <ReportTabsTrigger
              value="regionalKPI"
              tabName="Region"
            />
            <ReportTabsTrigger
              value="marketKPI"
              tabName="Market"
            />
          </ReportsTabsList>

          <TabsContent value="propertyCount">
            <ReportTabsContentUILayout
              loading={loading.rpm}
              error={error.rpm}
              component={
                <PropertyCountStaffingTable
                  rpmData={staffingRPMData}
                  datePeriod={datePeriod}
                  filters={filters}
                />
              }
            />
          </TabsContent>
          <TabsContent value="regionalKPI">
            <ReportTabsContentUILayout
              loading={loading.regional}
              error={error.regional}
              component={
                <RegionalAverageTable
                  regionalTableData={staffingRegionalData}
                  datePeriod={datePeriod}
                  filters={filters}
                />
              }
            />
          </TabsContent>
          <TabsContent value="marketKPI">
            <ReportTabsContentUILayout
              loading={loading.market}
              error={error.market}
              component={
                <MarketLevelStaffingTable
                  marketTableData={staffingMarketData}
                  datePeriod={datePeriod}
                  filters={filters}
                />
              }
            />
          </TabsContent>
        </Tabs>
      </div>
    </Container>
  );
}
