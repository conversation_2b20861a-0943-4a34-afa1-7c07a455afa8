import { StaffingMarketKPITypes } from '@/api/staffingKpi/staffingKpiApi.types';
import { ReportFilters } from '@/slice/incomeReportSlice';
import { FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  CommonTable,
  CommonTableBodyCell,
  CommonTableBodyRow,
  CommonTableBodyRowTotal,
  CommonTableBodyTotalCell,
  CommonTableHeadingCell,
  CommonTableHeadingMergeCell,
  CommonTableMainHeaderRow,
  CommonTableSubHeaderRow,
} from '../../ReportsCommonComponents/commonReportsTableAtoms/CommonReportsTable';
import { downloadExcelStaffingMarketKPI } from '../utils/exportDownloadFormattersStaffingKPI';
import { getStaffingMarketColumnTotals } from '../utils/helperStaffingKPI';

interface PropsTypesMarketTable {
  marketTableData: StaffingMarketKPITypes[];
  datePeriod: string;
  filters: ReportFilters;
}

const staffingMarketColumnWidth = 'w-[140px]';

export default function MarketLevelStaffingTable(props: PropsTypesMarketTable) {
  const { marketTableData, datePeriod, filters } = props;

  const sortAccendingOrder = marketTableData?.sort((a, b) =>
    (a?.RegionMarket ?? '')?.localeCompare(b.RegionMarket ?? ''),
  );

  const marketTotals = getStaffingMarketColumnTotals(sortAccendingOrder);

  return (
    <div>
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {datePeriod}
        </h3>
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
          onClick={() => {
            downloadExcelStaffingMarketKPI(sortAccendingOrder, filters);
          }}
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>

      <CommonTable>
        <thead>
          <CommonTableMainHeaderRow>
            <th></th>
            <CommonTableHeadingMergeCell colSpan={6}>
              Period-End Actuals
            </CommonTableHeadingMergeCell>
          </CommonTableMainHeaderRow>

          <CommonTableSubHeaderRow>
            {/* <th className={`${staffingMarketColumnWidth} text-left`}></th> */}
            <th className={`${staffingMarketColumnWidth}`}>{''}</th>
            <CommonTableHeadingCell
              className={`${staffingMarketColumnWidth}`}
              borderLeft
            >
              RPM's
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className={`${staffingMarketColumnWidth}`}>
              VP's
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className={`${staffingMarketColumnWidth}`}>
              Properties
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className={`${staffingMarketColumnWidth}`}>
              Properties/ RPM
            </CommonTableHeadingCell>
            {/* <CommonTableHeadingCell className={`${staffingMarketColumnWidth}`}>
              Workload/ RPM
            </CommonTableHeadingCell> */}
            <CommonTableHeadingCell
              className={`${staffingMarketColumnWidth}`}
              borderRight
            >
              RPM's/ VP
            </CommonTableHeadingCell>
          </CommonTableSubHeaderRow>
        </thead>

        <tbody>
          {sortAccendingOrder?.map((item) => {
            return (
              <CommonTableBodyRow key={item?.RegionMarket}>
                <td className="text-start px-1">{item?.RegionMarket}</td>
                {/* actuals */}
                <CommonTableBodyCell borderLeft={true}>
                  {item?.RPM_Actual?.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.VP_Actual?.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.Property_Actual?.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.PropertyPerRPM_Actual?.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </CommonTableBodyCell>
                {/* <CommonTableBodyCell>
                  {item?.WorkloadPerRPM_Actual}
                </CommonTableBodyCell> */}
                {/* <CommonTableBodyCell>
                  {item?.WorkloadPerRPM_Actual}
                </CommonTableBodyCell> */}
                <CommonTableBodyCell borderRight={true}>
                  {item?.RPMPerVP_Actual?.toLocaleString('en-US', {
                    // maximumFractionDigits: 1,
                    maximumFractionDigits: 0,
                  })}
                </CommonTableBodyCell>
              </CommonTableBodyRow>
            );
          })}
          <CommonTableBodyRowTotal>
            {/* <td className="text-start px-1 font-bold">Total</td> */}
            <CommonTableBodyTotalCell className="text-start ">
              TOTAL
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell borderLeft={true}>
              {marketTotals.rpm_actual?.toLocaleString('en-US', {
                maximumFractionDigits: 0,
              })}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell>
              {marketTotals.vp_actual?.toLocaleString('en-US', {
                maximumFractionDigits: 0,
              })}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell>
              {marketTotals.properties_actual?.toLocaleString('en-US', {
                maximumFractionDigits: 0,
              })}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell>
              {marketTotals.properties_rpm_actual?.toLocaleString('en-US', {
                maximumFractionDigits: 0,
              })}
            </CommonTableBodyTotalCell>

            {/* <CommonTableBodyTotalCell>
              {marketTotals.workload_rpm_actual?.toLocaleString('en-US', {maximumFractionDigits:1})}
            </CommonTableBodyTotalCell> */}
            {/* <CommonTableBodyTotalCell>
              {marketTotals.workload_rpm_actual?.toLocaleString('en-US', {maximumFractionDigits:1})}
            </CommonTableBodyTotalCell> */}

            <CommonTableBodyTotalCell borderRight={true}>
              {marketTotals.rpm_vp_actual?.toLocaleString('en-US', {
                maximumFractionDigits: 0,
              })}
            </CommonTableBodyTotalCell>
          </CommonTableBodyRowTotal>
        </tbody>
      </CommonTable>
    </div>
  );
}
