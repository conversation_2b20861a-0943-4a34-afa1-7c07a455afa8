import { StaffingRPMKPITypes } from '@/api/staffingKpi/staffingKpiApi.types';
import { ReportFilters } from '@/slice/incomeReportSlice';
import { FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  CommonTable,
  CommonTableBodyCell,
  CommonTableBodyRow,
  CommonTableBodyRowTotal,
  CommonTableBodyTotalCell,
  CommonTableHeadingCell,
  CommonTableSubHeaderRow,
} from '../../ReportsCommonComponents/commonReportsTableAtoms/CommonReportsTable';
import { downloadExcelStaffingPropertyCount } from '../utils/exportDownloadFormattersStaffingKPI';
import { getStaffingPropertyCountColumnsTotals } from '../utils/helperStaffingKPI';

interface PropsTypes {
  rpmData: StaffingRPMKPITypes[];
  datePeriod: string;
  filters: ReportFilters;
}

const propertyCountTableColumWidth = 'w-[140px]';

export default function PropertyCountStaffingTable(props: PropsTypes) {
  const { rpmData, datePeriod, filters } = props;
  const sortAccendingOrder = rpmData?.sort((a, b) =>
    (a.RPM ?? '')?.localeCompare(b.RPM ?? ''),
  );
  const propertyCountTotals =
    getStaffingPropertyCountColumnsTotals(sortAccendingOrder);
  return (
    <div>
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {datePeriod}
        </h3>
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
          onClick={() =>
            downloadExcelStaffingPropertyCount(sortAccendingOrder, filters)
          }
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>

      <CommonTable>
        <thead>
          <CommonTableSubHeaderRow>
            <th className={`${propertyCountTableColumWidth} text-start`}></th>
            <CommonTableHeadingCell
              borderLeft
              className={`${propertyCountTableColumWidth}`}
            >
              Stabilized
            </CommonTableHeadingCell>
            <CommonTableHeadingCell
              className={`${propertyCountTableColumWidth}`}
            >
              Lease-Up
            </CommonTableHeadingCell>

            <CommonTableHeadingCell
              className={`${propertyCountTableColumWidth}`}
            >
              Total Properties
            </CommonTableHeadingCell>
            <CommonTableHeadingCell
              borderRight
              className={`${propertyCountTableColumWidth}`}
            >
              Expected PM Fees
            </CommonTableHeadingCell>
          </CommonTableSubHeaderRow>
        </thead>
        <tbody>
          {sortAccendingOrder?.map((item) => {
            return (
              <CommonTableBodyRow key={item?.RPM}>
                <CommonTableBodyCell className="text-left">
                  {item?.RPM}
                </CommonTableBodyCell>

                <CommonTableBodyCell borderLeft>
                  {item?.Stabilised?.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.Lease_Up?.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </CommonTableBodyCell>

                <CommonTableBodyCell>
                  {item?.Total_Properties?.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </CommonTableBodyCell>
                <CommonTableBodyCell borderRight>
                  {item?.Expected_PM_Fees?.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                  {/* {item?.Expected_PM_Fees &&
                    Math.round(item?.Expected_PM_Fees)?.toLocaleString('en-US')} */}
                </CommonTableBodyCell>
              </CommonTableBodyRow>
            );
          })}

          <CommonTableBodyRowTotal>
            <CommonTableBodyTotalCell textAlign="text-start" borderRight>
              Total
            </CommonTableBodyTotalCell>
            <CommonTableBodyTotalCell>
              {propertyCountTotals?.stabilized?.toLocaleString('en-US', {
                maximumFractionDigits: 0,
              })}
            </CommonTableBodyTotalCell>
            <CommonTableBodyTotalCell>
              {propertyCountTotals?.lease_up?.toLocaleString('en-US', {
                maximumFractionDigits: 0,
              })}
            </CommonTableBodyTotalCell>
            <CommonTableBodyTotalCell>
              {propertyCountTotals?.total_properties?.toLocaleString('en-US', {
                maximumFractionDigits: 0,
              })}
            </CommonTableBodyTotalCell>
            <CommonTableBodyTotalCell borderRight>
              {propertyCountTotals?.expected_pm_fees?.toLocaleString('en-US', {
                maximumFractionDigits: 0,
              })}
              {/* {Math.round(
                propertyCountTotals?.expected_pm_fees,
              )?.toLocaleString('en-US')} */}
            </CommonTableBodyTotalCell>
          </CommonTableBodyRowTotal>
        </tbody>
      </CommonTable>
    </div>
  );
}
