import { StaffingRegionalKPITypes } from '@/api/staffingKpi/staffingKpiApi.types';
import { ReportFilters } from '@/slice/incomeReportSlice';
import { FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  CommonTable,
  CommonTableBodyCell,
  CommonTableBodyRow,
  CommonTableBodyRowTotal,
  CommonTableBodyTotalCell,
  CommonTableHeadingCell,
  CommonTableHeadingMergeCell,
  CommonTableMainHeaderRow,
  CommonTableSubHeaderRow,
} from '../../ReportsCommonComponents/commonReportsTableAtoms/CommonReportsTable';
import { downloadExcelStaffingRegionalKPI } from '../utils/exportDownloadFormattersStaffingKPI';
import {
  getStaffingRegionalColumnTotals,
  tableRowOrderFormateStaffingRegional,
} from '../utils/helperStaffingKPI';

interface PropsTypesRegionalTable {
  regionalTableData: StaffingRegionalKPITypes[];
  datePeriod: string;
  filters: ReportFilters;
}

const staffingRegionColumnsWidth = 'w-[140px]';

export default function RegionalAverageTable(props: PropsTypesRegionalTable) {
  const { regionalTableData, datePeriod, filters } = props;

  const totals = getStaffingRegionalColumnTotals(regionalTableData);
  return (
    <div>
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {datePeriod}
        </h3>
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
          onClick={() => {
            downloadExcelStaffingRegionalKPI(regionalTableData, filters);
          }}
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>
      <CommonTable>
        <thead>
          <CommonTableMainHeaderRow height="h-6">
            <th className={`${staffingRegionColumnsWidth}`}></th>
            <CommonTableHeadingMergeCell colSpan={8}>
              Regional Average Staffing KPI's
            </CommonTableHeadingMergeCell>
          </CommonTableMainHeaderRow>

          <CommonTableMainHeaderRow>
            <th></th>

            <CommonTableHeadingCell
              textAlign="text-center"
              colSpan={4}
              borderLeft
              borderRight
              className="border-b-1 border-black"
              fontSize="text-sm"
            >
              Period-End Actuals
            </CommonTableHeadingCell>
          </CommonTableMainHeaderRow>

          <CommonTableSubHeaderRow>
            <th className={`${staffingRegionColumnsWidth}`}></th>
            <CommonTableHeadingCell
              borderLeft
              className={`${staffingRegionColumnsWidth}`}
            >
              Central
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className={`${staffingRegionColumnsWidth}`}>
              East
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className={`${staffingRegionColumnsWidth}`}>
              West
            </CommonTableHeadingCell>
            <CommonTableHeadingCell
              className={`${staffingRegionColumnsWidth}`}
              borderRight
            >
              Consol.
            </CommonTableHeadingCell>
          </CommonTableSubHeaderRow>
        </thead>

        <tbody>
          {tableRowOrderFormateStaffingRegional?.map(({ label, key }) => {
            const item = regionalTableData?.find(
              (item) => item?.parameter_name === key,
            );
            return (
              <CommonTableBodyRow key={label}>
                <td>
                  <div className="text-left px-1">{label}</div>
                </td>


                <CommonTableBodyCell borderLeft>
                  {item?.central_actual?.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.east_actual?.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.west_actual?.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </CommonTableBodyCell>
                <CommonTableBodyCell borderRight>
                  {item?.consolidated_actual?.toLocaleString('en-US', {
                    maximumFractionDigits: 0,
                  })}
                </CommonTableBodyCell>
              </CommonTableBodyRow>
            );
          })}

          {/* <CommonTableBodyRowTotal>
            <CommonTableBodyTotalCell textAlign="text-start">
              Total
            </CommonTableBodyTotalCell>
            <CommonTableBodyTotalCell borderLeft>
              {totals?.central_actual?.toLocaleString('en-US', {
                maximumFractionDigits: 1,
              })}
            </CommonTableBodyTotalCell>
            <CommonTableBodyTotalCell>
              {' '}
              {totals?.east_actual?.toLocaleString('en-US', {
                maximumFractionDigits: 1,
              })}
            </CommonTableBodyTotalCell>
            <CommonTableBodyTotalCell>
              {totals?.west_actual?.toLocaleString('en-US', {
                maximumFractionDigits: 1,
              })}
            </CommonTableBodyTotalCell>
            <CommonTableBodyTotalCell borderRight>
              {totals?.consolidated_actual?.toLocaleString('en-US', {
                maximumFractionDigits: 1,
              })}
            </CommonTableBodyTotalCell>
          </CommonTableBodyRowTotal> */}
        </tbody>
      </CommonTable>
    </div>
  );
}
