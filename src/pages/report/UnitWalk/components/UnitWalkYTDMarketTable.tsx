import { UnitWalkKPIYTDMarketTypes } from '@/api/unitWalkApis/unitWalkTypes';
import { ReportFilters } from '@/slice/incomeReportSlice';
import { FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  CommonTable,
  CommonTableBodyCell,
  CommonTableBodyRow,
  CommonTableBodyRowTotal,
  CommonTableBodyTotalCell,
  CommonTableHeadingCell,
  CommonTableMainHeaderRow,
} from '../../ReportsCommonComponents/commonReportsTableAtoms/CommonReportsTable';
import { downloadExcelUnitWalkYTDMarket } from '../utils/exportDownloadFormattersUnitWalk';
import { sumUnitWalkMarket } from '../utils/unitWalkHelpers';

interface UnitWalkYTDMarketTableProps {
  marketData: UnitWalkKPIYTDMarketTypes[];
  datePeriod: string;
  filters: ReportFilters;
}
const columnWidth = 'w-[140px]';

export default function UnitWalkYTDMarketTable(
  props: UnitWalkYTDMarketTableProps,
) {
  const { marketData, datePeriod, filters } = props;

  const sortAscendingOrder = marketData?.sort((a, b) =>
    a.RegionMarket.localeCompare(b.RegionMarket),
  );

  const unitWalkSum = sumUnitWalkMarket(sortAscendingOrder);

  const exportUnitWalkMarket = () => {
    downloadExcelUnitWalkYTDMarket(sortAscendingOrder, filters);
  };

  return (
    <div>
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {datePeriod}
        </h3>
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
          onClick={exportUnitWalkMarket}
          disabled={!sortAscendingOrder?.length}
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>

      <div>
        <CommonTable>
          <thead>
            <CommonTableMainHeaderRow className="border-t-2 border-black">
              <CommonTableHeadingCell
                className={`${columnWidth}`}
                textAlign="text-start"
                borderLeft
              >
                Market
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className={`${columnWidth}`}>
                Beginning
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className={`${columnWidth}`}>
                Additions
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className={`${columnWidth}`}>
                Losses
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className={`${columnWidth}`} borderRight>
                Current
              </CommonTableHeadingCell>
            </CommonTableMainHeaderRow>
          </thead>
          <tbody>
            {sortAscendingOrder?.map((marketItem) => {
              return (
                <CommonTableBodyRow
                  key={marketItem?.RegionMarket}
                  lastRowBorder={false}
                >
                  <CommonTableBodyCell textAlign="text-start" borderLeft>
                    {marketItem?.RegionMarket}
                  </CommonTableBodyCell>
                  <CommonTableBodyCell>
                    {marketItem?.beginning?.toLocaleString('en-Us', {
                      maximumFractionDigits: 0,
                    })}
                  </CommonTableBodyCell>

                  <CommonTableBodyCell>
                    {marketItem?.additions?.toLocaleString('en-US', {
                      maximumFractionDigits: 0,
                    })}
                  </CommonTableBodyCell>
                  <CommonTableBodyCell>
                    {marketItem?.losses?.toLocaleString('en-US', {
                      maximumFractionDigits: 0,
                    })}
                  </CommonTableBodyCell>
                  <CommonTableBodyCell borderRight>
                    {marketItem?.current?.toLocaleString('en-US', {
                      maximumFractionDigits: 0,
                    })}
                  </CommonTableBodyCell>
                </CommonTableBodyRow>
              );
            })}

            <CommonTableBodyRowTotal
              lastRowBorder={false}
              className="border-b-2 border-black"
            >
              <CommonTableBodyTotalCell textAlign="text-start" borderLeft>
                Total
              </CommonTableBodyTotalCell>
              <CommonTableBodyTotalCell>
                {unitWalkSum?.beginning?.toLocaleString('en-US', {
                  maximumFractionDigits: 0,
                })}
              </CommonTableBodyTotalCell>
              <CommonTableBodyTotalCell>
                {unitWalkSum?.additions?.toLocaleString('en-US', {
                  maximumFractionDigits: 0,
                })}
              </CommonTableBodyTotalCell>
              <CommonTableBodyTotalCell>
                {unitWalkSum?.losses?.toLocaleString('en-US', {
                  maximumFractionDigits: 0,
                })}
              </CommonTableBodyTotalCell>
              <CommonTableBodyTotalCell borderRight>
                {unitWalkSum?.current?.toLocaleString('en-US', {
                  maximumFractionDigits: 0,
                })}
              </CommonTableBodyTotalCell>
            </CommonTableBodyRowTotal>
          </tbody>
        </CommonTable>
      </div>
    </div>
  );
}
