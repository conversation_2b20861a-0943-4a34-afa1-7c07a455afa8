import React, { useEffect, useState } from 'react';
import { getPropertyPackageFilterOptions } from '@/api/propertyPackageApi';
import { setFilters } from '@/slice/incomeReportSlice';
import { RootState } from '@/store';
import { useDispatch, useSelector } from 'react-redux';
import MultiSelect from '@/components/ui/MultiSelect';
import SingleSelect from '@/components/ui/SingleSelect';

interface PropsTypes {
  businessFilter?: boolean;
}

const PropertyPackageReportFilters: React.FC<PropsTypes> = ({ businessFilter = true }) => {
  const dispatch = useDispatch();
  const reduxFilters = useSelector(
    (state: RootState) => state.incomeReport.filters,
  );

  const [localFilters, setLocalFilters] = useState(reduxFilters);

  const [filterOptions, setFilterOptions] = useState({
    years: [] as number[],
    months: [] as number[],
    departments: [] as string[],
    businessTypes: [] as string[],
    marketleaders: [] as string[],
    adminBu: [] as string[],
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchFilterOptions = async () => {
      setLoading(true);
      try {
        const options = await getPropertyPackageFilterOptions();
        setFilterOptions(options);
      } catch (error) {
        console.error('Error fetching property package filter options:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFilterOptions();
  }, []);

  useEffect(() => {
    setLocalFilters(reduxFilters);
  }, [reduxFilters]);

  const sortMonths = (months: string[]) => {
    const monthOrder = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return months.sort((a, b) => monthOrder.indexOf(a) - monthOrder.indexOf(b));
  };

  const handleLocalFilterChange = (filterName: string, values: string[]) => {
    const processedValues =
      filterName === 'month' ? sortMonths(values) : values;
    setLocalFilters({ ...localFilters, [filterName]: processedValues });
  };

  const handleLocalYearChange = (value: string) => {
    setLocalFilters({ ...localFilters, year: value });
  };

  const handleApplyChanges = () => {
    dispatch(setFilters(localFilters));
  };

  const handleReset = () => {
    const defaultFilters = {
      year: '2025',
      month: ['January'],
      businessType: [],
      department: [],
      marketLeader: [],
      adminBU: [],
    };
    setLocalFilters(defaultFilters);
  };

  const hasChanges =
    JSON.stringify(localFilters) !== JSON.stringify(reduxFilters);

  const getDefaultYears = () => ['2025', '2024', '2023'];
  const getDefaultMonths = () => [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  const getMonthOptions = () => {
    return getDefaultMonths();
  };

  return (
    <div className="flex items-end gap-4 w-full">
      <div className="w-24">
        <div className="flex flex-col">
          <div className="flex items-center gap-2 mb-1">
            <label className="text-sm font-medium">Year :</label>
            {loading && (
              <div className="animate-spin rounded-full h-3 w-3 border border-gray-300 border-t-[#43298F]"></div>
            )}
          </div>
          <SingleSelect
            id="year"
            label=""
            options={
              filterOptions.years.length > 0
                ? filterOptions.years.map(String)
                : getDefaultYears()
            }
            selectedValue={localFilters.year}
            onChange={handleLocalYearChange}
            disabled={loading}
            placeholder="Select year"
          />
        </div>
      </div>

      <div className="w-32">
        <div className="flex flex-col">
          <div className="flex items-center gap-2 mb-1">
            <label className="text-sm font-medium">Month :</label>
            {loading && (
              <div className="animate-spin rounded-full h-3 w-3 border border-gray-300 border-t-[#43298F]"></div>
            )}
          </div>
          <MultiSelect
            id="month"
            label=""
            options={getMonthOptions()}
            selectedValues={localFilters.month}
            onChange={(values) => handleLocalFilterChange('month', values)}
            disabled={loading}
          />
        </div>
      </div>

      {businessFilter && (
        <div className="flex-1">
          <div className="flex flex-col">
            <div className="flex items-center gap-2 mb-1">
              <label className="text-sm font-medium">Business Type</label>
              {loading && (
                <div className="animate-spin rounded-full h-3 w-3 border border-gray-300 border-t-[#43298F]"></div>
              )}
            </div>
            <MultiSelect
              id="businessType"
              label=""
              options={filterOptions.businessTypes}
              selectedValues={localFilters.businessType}
              onChange={(values) =>
                handleLocalFilterChange('businessType', values)
              }
              disabled={loading}
            />
          </div>
        </div>
      )}

      <div className="flex-1">
        <div className="flex flex-col">
          <div className="flex items-center gap-2 mb-1">
            <label className="text-sm font-medium">Region</label>
            {loading && (
              <div className="animate-spin rounded-full h-3 w-3 border border-gray-300 border-t-[#43298F]"></div>
            )}
          </div>
          <MultiSelect
            id="department"
            label=""
            options={filterOptions.departments}
            selectedValues={localFilters.department}
            onChange={(values) => handleLocalFilterChange('department', values)}
            disabled={loading}
          />
        </div>
      </div>

      <div className="flex-1">
        <div className="flex flex-col">
          <div className="flex items-center gap-2 mb-1">
            <label className="text-sm font-medium">Market Leader</label>
            {loading && (
              <div className="animate-spin rounded-full h-3 w-3 border border-gray-300 border-t-[#43298F]"></div>
            )}
          </div>
          <MultiSelect
            id="marketLeader"
            label=""
            options={filterOptions.marketleaders}
            selectedValues={localFilters.marketLeader}
            onChange={(values) =>
              handleLocalFilterChange('marketLeader', values)
            }
            disabled={loading}
          />
        </div>
      </div>

      <div className="flex-1">
        <div className="flex flex-col">
          <div className="flex items-center gap-2 mb-1">
            <label className="text-sm font-medium">Admin BU</label>
            {loading && (
              <div className="animate-spin rounded-full h-3 w-3 border border-gray-300 border-t-[#43298F]"></div>
            )}
          </div>
          <MultiSelect
            id="adminBU"
            label=""
            options={filterOptions.adminBu}
            selectedValues={localFilters.adminBU}
            onChange={(values) => handleLocalFilterChange('adminBU', values)}
            disabled={loading}
          />
        </div>
      </div>

      <div className="flex items-center gap-3 ml-4">
        <button
          onClick={handleApplyChanges}
          disabled={!hasChanges || loading}
          className="px-6 py-2 text-white font-medium rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          style={{
            backgroundColor: '#43298F',
            opacity: !hasChanges || loading ? 0.5 : 1,
          }}
        >
          Apply
        </button>

        <button
          onClick={handleReset}
          disabled={loading}
          className="px-6 py-2 font-medium rounded-md border-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          style={{
            color: '#43298F',
            borderColor: '#43298F',
            backgroundColor: 'white',
          }}
        >
          Reset
        </button>
      </div>
    </div>
  );
};

export default PropertyPackageReportFilters;